# net
## UDS基础组件实现计划

### 第一阶段：扩展基础组件支持UDS ✅
- [x] 任务1：扩展Address类支持Unix域地址
  - 文件：`cpp/include/zexuan/net/address.hpp`, `cpp/source/zexuan/net/address.cpp`
  - 添加sockaddr_un成员变量和Unix域地址构造函数
  - 修改family()、getSockAddr()等方法支持AF_UNIX
  - 预期成果：Address类能够处理Unix域地址，提供路径设置和获取功能 ✅

- [x] 任务2：扩展sockets_ops支持UDS系统调用
  - 文件：`cpp/include/zexuan/net/sockets_ops.hpp`, `cpp/source/zexuan/net/sockets_ops.cpp`
  - 添加UDS特定函数：createUdsNonblockingOrDie, bindUdsOrDie, connectUds, acceptUds等
  - 修改现有函数支持AF_UNIX family和sockaddr_un长度计算
  - 预期成果：sockets_ops提供完整的UDS系统调用封装 ✅

- [x] 任务3：扩展Socket类支持UDS操作
  - 文件：`cpp/include/zexuan/net/socket.hpp`, `cpp/source/zexuan/net/socket.cpp`
  - 添加UDS特定方法：bindUdsAddress, acceptUds, isUnixSocket等
  - 修改bindAddress和accept方法支持Unix域地址
  - 预期成果：Socket类能够处理UDS套接字操作 ✅

### 第二阶段：实现UDS网络组件 ✅
- [x] 任务4：实现UdsAcceptor
  - 文件：`cpp/include/zexuan/net/uds_acceptor.hpp`, `cpp/source/zexuan/net/uds_acceptor.cpp`
  - 复用Acceptor架构，使用Unix域地址监听
  - 处理Unix socket文件的创建和清理
  - 预期成果：UdsAcceptor能够监听Unix socket路径，接受新连接 ✅

- [x] 任务5：实现UdsConnector
  - 文件：`cpp/include/zexuan/net/uds_connector.hpp`, `cpp/source/zexuan/net/uds_connector.cpp`
  - 复用Connector连接管理逻辑，支持Unix域地址连接
  - 提供重连机制和连接状态管理（定时器功能待实现）
  - 预期成果：UdsConnector能够连接到Unix socket路径 ✅

- [x] 任务6：实现UdsConnection
  - 文件：`cpp/include/zexuan/net/uds_connection.hpp`, `cpp/source/zexuan/net/uds_connection.cpp`
  - 复用TcpConnection的连接管理和数据处理逻辑
  - 适配Unix域地址信息，使用UDS专用回调接口
  - 预期成果：UdsConnection提供与TcpConnection一致的接口和功能 ✅

### 第三阶段：实现高层UDS组件 ✅
- [x] 任务7：实现UdsServer
  - 文件：`cpp/include/zexuan/net/uds_server.hpp`, `cpp/source/zexuan/net/uds_server.cpp`
  - 复用TcpServer架构，使用UdsAcceptor监听，管理UdsConnection
  - 处理Unix socket文件的生命周期
  - 预期成果：UdsServer提供与TcpServer一致的服务器功能 ✅

- [x] 任务8：实现UdsClient
  - 文件：`cpp/include/zexuan/net/uds_client.hpp`, `cpp/source/zexuan/net/uds_client.cpp`
  - 复用TcpClient客户端逻辑，使用UdsConnector连接
  - 管理UdsConnection，提供重连和断线处理
  - 预期成果：UdsClient提供与TcpClient一致的客户端功能 ✅

### 第四阶段：测试和验证
- [x] 👤 任务9：编写UDS组件单元测试
  - 文件：`cpp/test/source/uds_*_test.cpp`
  - 测试Address类Unix域地址功能、UDS系统调用封装、连接和数据传输
  - 预期成果：完整的测试覆盖，确保UDS组件功能正确 ✅
  - 已创建：uds_address_test.cpp, uds_sockets_ops_test.cpp, uds_socket_test.cpp, uds_integration_test.cpp

- [x] 👤 任务10：编写UDS示例程序
  - 文件：`cpp/examples/uds_echo_server.cpp`, `cpp/examples/uds_echo_client.cpp`
  - UDS回显服务器/客户端示例，使用cout直接输出日志
  - 预期成果：可运行的示例程序，验证UDS组件可用性 ✅

### 任务依赖关系
```
任务1(Address扩展) → 任务2(sockets_ops扩展) → 任务3(Socket扩展)
                                                      ↓
任务4(UdsAcceptor) ← 任务5(UdsConnector) ← 任务6(UdsConnection)
        ↓                    ↓                    ↓
任务7(UdsServer) ←——————————— 任务8(UdsClient)
        ↓                    ↓
任务9(单元测试) ←——————————— 任务10(示例程序)
```
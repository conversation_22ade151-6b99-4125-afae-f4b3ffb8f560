# TCP消息总线系统开发计划书

## 1. 项目概述

### 1.1 项目信息
- **项目名称**: 基于muduo的TCP消息总线系统
- **项目目标**: 开发一个轻量级、高性能的TCP消息总线，支持微服务间通信，基于发布-订阅模式，使用JSON协议进行消息传输
- **技术栈**: C++17, muduo网络库, nlohmann/json, spdlog日志库
- **目标平台**: Linux环境
- **部署方式**: 独立程序运行

### 1.2 核心特性
- ✅ 基于MessageType和EventType的发布-订阅消息路由
- ✅ 支持CommonMessage和EventMessage两种消息类型
- ✅ 使用JSON格式进行消息序列化和传输
- ✅ JSON配置文件管理
- ✅ 基于spdlog的日志系统
- ✅ 高并发连接支持

## 2. JSON协议设计

### 2.1 配置文件格式 (config.json)

```json
{
    "bus": {
        "server": {
            "host": "0.0.0.0",
            "port": 8080,
            "thread_pool_size": 4
        }
    }
}
```

### 2.2 订阅/取消订阅消息格式

```json
{
  "action": "subscribe",  // "subscribe" | "unsubscribe"
  "message_types": [1, 2, 3],  // MessageType数组
  "event_types": [100, 200, 300]  // EventType数组 (可选)
}
```

### 2.3 CommonMessage JSON格式

```json
{
  "msg_category": "common",
  "type": 1,
  "source_id": 12345,
  "target_id": 67890,
  "invoke_id": "req_001",
  "data": "base64编码的二进制数据",
  "is_last_msg": true
}
```

### 2.4 EventMessage JSON格式

```json
{
  "msg_category": "event",
  "event_type": 100,
  "device_uuid": {
    "device_id": 1001,
    "category": 1
  },
  "source_id": 12345,
  "description": "设备状态变化",
  "data": "base64编码的二进制数据"
}
```

### 2.5 系统控制消息格式

```json
{
  "msg_category": "control",
  "action": "subscribe_response",
  "success": true,
  "message": "订阅成功",
  "subscribed_message_types": [1, 2],
  "subscribed_event_types": [100, 200]
}
```

## 3. 系统架构设计

### 3.1 整体架构图

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   TCP Client    │    │   TCP Client    │    │   TCP Client    │
│   (Publisher)   │    │  (Subscriber)   │    │  (Subscriber)   │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
                    ┌─────────────▼─────────────┐
                    │     TCP Message Bus      │
                    │                          │
                    │  ┌─────────────────────┐ │
                    │  │  Connection Mgr    │ │
                    │  └─────────────────────┘ │
                    │  ┌─────────────────────┐ │
                    │  │  Subscription Mgr  │ │
                    │  └─────────────────────┘ │
                    │  ┌─────────────────────┐ │
                    │  │  Message Router    │ │
                    │  └─────────────────────┘ │
                    │  ┌─────────────────────┐ │
                    │  │  JSON Serializer   │ │
                    │  └─────────────────────┘ │
                    └──────────────────────────┘
```

### 3.2 核心组件说明

| 组件 | 职责 | 实现状态 |
|------|------|----------|
| Connection Mgr | 管理TCP连接生命周期 | ✅ 已实现 |
| Subscription Mgr | 管理客户端订阅信息 | 🔄 开发中 |
| Message Router | 基于订阅规则分发消息 | 🔄 开发中 |
| JSON Serializer | 消息序列化/反序列化 | 🔄 开发中 |

## 4. 详细任务分解

### 4.1 阶段一：基础架构搭建 ✅

#### 任务1.1: 项目结构初始化 ✅
- [x] 创建CMakeLists.txt配置文件
- [x] 设置依赖库（zexuan_base, zexuan_net）
- [x] 建立基本的目录结构

#### 任务1.2: 配置管理模块开发 ✅
- [x] 直接传入路径，成员变量保存配置文件读取的内容
- [x] 支持JSON格式配置文件

#### 任务1.3: 日志系统集成 ✅
- [x] 直接使用spdlog::info之类的接口
- [x] 使用logger_manager进行初始化
- [x] 解决多线程日志析构顺序问题
### 4.2 阶段二：消息协议实现 🔄

#### 任务2.1: JSON序列化模块 🔄
- [x] 实现CommonMessage与JSON的相互转换
- [x] 实现EventMessage与JSON的相互转换
- [x] 实现订阅消息的JSON解析
- [x] 添加Base64编码/解码支持

#### 任务2.2: 消息协议定义 🔄
- [x] 定义TCP传输层协议（消息长度+JSON内容）
- [x] 实现消息打包和解包功能
- [x] 添加消息完整性校验
### 4.3 阶段三：核心业务逻辑 🔄

#### 任务3.1: 连接管理器 ✅
- [x] 基于muduo实现TCP服务器
- [x] 管理客户端连接的生命周期
- [x] 实现连接状态监控

#### 任务3.2: 订阅管理器 🔄
- [ ] 实现客户端订阅信息存储
- [x] 支持按MessageType和EventType订阅
- [x] 实现订阅/取消订阅逻辑

#### 任务3.3: 消息路由器 🔄
- [x] 实现基于订阅规则的消息分发
- [x] 支持点对点和广播消息

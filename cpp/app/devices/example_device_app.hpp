/**
 * @file example_device_app.hpp
 * @brief 示例设备应用程序
 * <AUTHOR>
 * @date 2025-08-25
 */

#ifndef DEVICES_EXAMPLE_DEVICE_APP_HPP
#define DEVICES_EXAMPLE_DEVICE_APP_HPP

#include "include/devices_base.hpp"
#include <spdlog/spdlog.h>

namespace devices {

/**
 * @brief 示例事件上报器
 * 
 * 继承自BaseEventReporter，实现自定义事件上报
 */
class ExampleEventReporter : public BaseEventReporter {
public:
    ExampleEventReporter(zexuan::bus::TcpBusClient& client, 
                        zexuan::net::EventLoop& event_loop,
                        double interval_seconds = 5.0)
        : BaseEventReporter(client, event_loop, interval_seconds) {}

protected:
    void generateEvent() override {
        // 创建自定义事件
        auto event_msg = createBaseEventMessage(2, "Example device status event");
        
        // 添加设备状态数据
        std::string status_data = "device_status:online,temperature:25.5,humidity:60.2";
        event_msg.data.assign(status_data.begin(), status_data.end());
        
        // 发送事件
        sendEventMessage(event_msg);
    }
};

/**
 * @brief 示例消息处理器
 * 
 * 继承自BaseMessageProcessor，处理特定类型的消息
 */
class ExampleMessageProcessor : public BaseMessageProcessor {
public:
    explicit ExampleMessageProcessor(zexuan::bus::TcpBusClient& client) 
        : BaseMessageProcessor(client) {}

    bool processMessage(const zexuan::base::CommonMessage& original_message,
                       const zexuan::base::Message& input_msg) override {
        spdlog::info("Example processor handling message type {:02X}", input_msg.getTyp());
        
        // 创建响应消息
        std::string response_content = "Example response to: " + input_msg.getTextContent();
        auto response_msg = createResponse(input_msg, 0x07, response_content);
        
        // 序列化响应消息
        std::vector<uint8_t> response_data;
        size_t size = response_msg.serialize(response_data);
        
        if (size > 0) {
            auto response = createBaseResponse(original_message);
            response.data = response_data;
            
            bool success = sendResponse(response);
            if (success) {
                spdlog::debug("Successfully sent example response");
            } else {
                spdlog::error("Failed to send example response");
            }
            return success;
        }
        
        return false;
    }

    bool canProcess(uint8_t message_type) const override {
        // 处理类型5的消息
        return message_type == 5;
    }

    std::string getName() const override {
        return "ExampleMessageProcessor";
    }
};

/**
 * @brief 示例设备应用程序
 * 
 * 继承自BaseClientApp，实现完整的设备应用程序
 */
class ExampleDeviceApp : public BaseClientApp {
public:
    ExampleDeviceApp(zexuan::net::EventLoop& event_loop,
                     const std::string& server_host,
                     uint16_t server_port,
                     const std::string& client_name = "ExampleDevice",
                     int client_id = 100)
        : BaseClientApp(event_loop, server_host, server_port, client_name, client_id) {}

protected:
    bool initialize() override {
        spdlog::info("Initializing example device app...");
        
        // 添加示例消息处理器
        addProcessor(std::make_unique<ExampleMessageProcessor>(getClient()));
        
        // 设置示例事件上报器
        setEventReporter(std::make_unique<ExampleEventReporter>(getClient(), getEventLoop(), 5.0));
        
        spdlog::info("Example device app initialized successfully");
        return true;
    }

    std::vector<int> getSubscribedMessageTypes() const override {
        // 订阅消息类型5
        return {5};
    }

    std::vector<int> getSubscribedEventTypes() const override {
        // 订阅事件类型2
        return {2};
    }

    void cleanup() override {
        spdlog::info("Cleaning up example device app...");
        // 在这里可以添加特定的清理逻辑
    }
};

} // namespace devices

#endif // DEVICES_EXAMPLE_DEVICE_APP_HPP

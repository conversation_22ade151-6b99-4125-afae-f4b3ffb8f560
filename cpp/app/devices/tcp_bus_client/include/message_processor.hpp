/**
 * @file message_processor.hpp
 * @brief 消息处理器基类定义
 * <AUTHOR>
 * @date 2025-08-25
 */

#ifndef TCP_BUS_CLIENT_MESSAGE_PROCESSOR_HPP
#define TCP_BUS_CLIENT_MESSAGE_PROCESSOR_HPP

#include "../../include/base_message_processor.hpp"

namespace tcp_bus_client {

/**
 * @brief 抽象消息处理器基类
 *
 * 继承自BaseMessageProcessor，定义了处理IEC103消息的通用接口
 */
class MessageProcessor : public devices::BaseMessageProcessor {
public:
    /**
     * @brief 构造函数
     * @param client TCP总线客户端引用
     */
    explicit MessageProcessor(zexuan::bus::TcpBusClient& client) : devices::BaseMessageProcessor(client) {}

    /**
     * @brief 虚析构函数
     */
    virtual ~MessageProcessor() = default;


};

} // namespace tcp_bus_client

#endif // TCP_BUS_CLIENT_MESSAGE_PROCESSOR_HPP

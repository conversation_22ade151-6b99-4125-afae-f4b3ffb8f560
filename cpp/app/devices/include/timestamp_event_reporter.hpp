/**
 * @file timestamp_event_reporter.hpp
 * @brief 时间戳事件上报器实现示例
 * <AUTHOR>
 * @date 2025-08-25
 */

#ifndef DEVICES_TIMESTAMP_EVENT_REPORTER_HPP
#define DEVICES_TIMESTAMP_EVENT_REPORTER_HPP

#include "base_event_reporter.hpp"
#include <chrono>

namespace devices {

/**
 * @brief 时间戳事件上报器
 * 
 * 继承自BaseEventReporter，实现定时发送时间戳事件的功能
 */
class TimestampEventReporter : public BaseEventReporter {
public:
    /**
     * @brief 构造函数
     * @param client TCP总线客户端引用
     * @param event_loop 事件循环引用
     * @param interval_seconds 上报间隔（秒）
     */
    TimestampEventReporter(zexuan::bus::TcpBusClient& client, 
                          zexuan::net::EventLoop& event_loop,
                          double interval_seconds = 3.0)
        : BaseEventReporter(client, event_loop, interval_seconds) {}

protected:
    /**
     * @brief 生成并发送时间戳事件
     */
    void generateEvent() override {
        // 创建时间戳事件
        auto event_msg = createBaseEventMessage(1, "Automatic timestamp event");

        // 添加当前时间戳作为数据
        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);
        std::string timestamp = std::to_string(time_t);
        event_msg.data.assign(timestamp.begin(), timestamp.end());

        // 发送事件
        sendEventMessage(event_msg);
    }
};

} // namespace devices

#endif // DEVICES_TIMESTAMP_EVENT_REPORTER_HPP

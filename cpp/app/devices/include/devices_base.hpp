/**
 * @file devices_base.hpp
 * @brief 设备基类总头文件
 * <AUTHOR>
 * @date 2025-08-25
 */

#ifndef DEVICES_BASE_HPP
#define DEVICES_BASE_HPP

#include "base_event_reporter.hpp"
#include "base_message_processor.hpp"
#include "base_client_app.hpp"

/**
 * @namespace devices
 * @brief 设备基类命名空间
 * 
 * 包含所有设备相关的基类定义：
 * - BaseEventReporter: 事件上报器基类
 * - BaseMessageProcessor: 消息处理器基类  
 * - BaseClientApp: 客户端应用程序基类
 */

#endif // DEVICES_BASE_HPP

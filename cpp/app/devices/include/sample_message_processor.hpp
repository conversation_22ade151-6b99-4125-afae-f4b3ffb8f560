/**
 * @file sample_message_processor.hpp
 * @brief 示例消息处理器实现
 * <AUTHOR>
 * @date 2025-08-25
 */

#ifndef DEVICES_SAMPLE_MESSAGE_PROCESSOR_HPP
#define DEVICES_SAMPLE_MESSAGE_PROCESSOR_HPP

#include "base_message_processor.hpp"
#include <spdlog/spdlog.h>

namespace devices {

/**
 * @brief 示例消息处理器
 * 
 * 继承自BaseMessageProcessor，实现处理特定类型消息的功能
 */
class SampleMessageProcessor : public BaseMessageProcessor {
public:
    /**
     * @brief 构造函数
     * @param client TCP总线客户端引用
     * @param supported_type 支持的消息类型
     */
    SampleMessageProcessor(zexuan::bus::TcpBusClient& client, uint8_t supported_type)
        : BaseMessageProcessor(client), supported_type_(supported_type) {}

    /**
     * @brief 处理接收到的消息
     * @param original_message 原始的CommonMessage
     * @param input_msg 解析后的消息
     * @return true表示处理成功，false表示处理失败
     */
    bool processMessage(const zexuan::base::CommonMessage& original_message,
                       const zexuan::base::Message& input_msg) override {
        spdlog::info("Processing message type {:02X} with processor {}", input_msg.getTyp(), getName());

        // 创建响应消息
        auto response_msg = createResponse(input_msg, 0x07, "Sample response content");
        
        // 序列化响应消息
        std::vector<uint8_t> response_data;
        response_msg.serialize(response_data);

        // 创建CommonMessage响应
        auto response = createBaseResponse(original_message);
        response.data = response_data;

        // 发送响应
        bool success = sendResponse(response);
        if (success) {
            spdlog::debug("Successfully sent response for message type {:02X}", input_msg.getTyp());
        } else {
            spdlog::error("Failed to send response for message type {:02X}", input_msg.getTyp());
        }

        return success;
    }

    /**
     * @brief 检查是否可以处理指定类型的消息
     * @param message_type 消息类型
     * @return true表示可以处理，false表示不能处理
     */
    bool canProcess(uint8_t message_type) const override {
        return message_type == supported_type_;
    }

    /**
     * @brief 获取处理器名称
     * @return 处理器名称
     */
    std::string getName() const override {
        return "SampleMessageProcessor_Type" + std::to_string(supported_type_);
    }

private:
    uint8_t supported_type_;  ///< 支持的消息类型
};

} // namespace devices

#endif // DEVICES_SAMPLE_MESSAGE_PROCESSOR_HPP

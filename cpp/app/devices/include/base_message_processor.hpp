/**
 * @file base_message_processor.hpp
 * @brief 消息处理器基类定义
 * <AUTHOR>
 * @date 2025-08-25
 */

#ifndef DEVICES_BASE_MESSAGE_PROCESSOR_HPP
#define DEVICES_BASE_MESSAGE_PROCESSOR_HPP

#include "zexuan/base/message.hpp"
#include "zexuan/base/types/structs.hpp"
#include "zexuan/bus/tcp_bus_client.hpp"

namespace devices {

/**
 * @brief 抽象消息处理器基类
 * 
 * 定义了处理消息的通用接口，所有具体的消息处理器都应该继承此类
 */
class BaseMessageProcessor {
public:
    /**
     * @brief 构造函数
     * @param client TCP总线客户端引用
     */
    explicit BaseMessageProcessor(zexuan::bus::TcpBusClient& client) : client_(client) {}

    /**
     * @brief 虚析构函数
     */
    virtual ~BaseMessageProcessor() = default;

    /**
     * @brief 处理接收到的消息（纯虚函数）
     * @param original_message 原始的CommonMessage
     * @param input_msg 解析后的消息
     * @return true表示处理成功，false表示处理失败
     */
    virtual bool processMessage(const zexuan::base::CommonMessage& original_message,
                               const zexuan::base::Message& input_msg) = 0;

    /**
     * @brief 检查是否可以处理指定类型的消息（纯虚函数）
     * @param message_type 消息类型
     * @return true表示可以处理，false表示不能处理
     */
    virtual bool canProcess(uint8_t message_type) const = 0;

    /**
     * @brief 获取处理器名称（纯虚函数）
     * @return 处理器名称
     */
    virtual std::string getName() const = 0;

protected:
    /**
     * @brief 创建基础响应消息
     * @param original_message 原始消息
     * @param is_last_message 是否为最后一帧消息
     * @return 创建的响应消息
     */
    zexuan::base::CommonMessage createBaseResponse(const zexuan::base::CommonMessage& original_message,
                                                  bool is_last_message = true) const {
        zexuan::base::CommonMessage response;
        response.type = zexuan::base::MessageType::RESULT;
        response.source_id = client_.getClientId();
        response.target_id = original_message.source_id;
        response.invoke_id = original_message.invoke_id;
        response.b_lastmsg = is_last_message;
        return response;
    }

    /**
     * @brief 创建响应消息
     * @param input_msg 输入消息
     * @param cot 传送原因
     * @param content 响应内容
     * @return 创建的响应消息
     */
    zexuan::base::Message createResponse(const zexuan::base::Message& input_msg,
                                        uint8_t cot,
                                        const std::string& content) const {
        zexuan::base::Message response_msg;
        response_msg.setTyp(input_msg.getTyp());
        response_msg.setVsq(input_msg.getVsq());
        response_msg.setCot(cot);
        response_msg.setSource(input_msg.getTarget());
        response_msg.setTarget(input_msg.getSource());
        response_msg.setFun(input_msg.getFun());
        response_msg.setInf(input_msg.getInf());
        response_msg.setTextContent(content);
        return response_msg;
    }

    /**
     * @brief 发送响应消息
     * @param response 要发送的响应消息
     * @return true表示发送成功，false表示发送失败
     */
    bool sendResponse(const zexuan::base::CommonMessage& response) const {
        return client_.sendCommonMessage(response);
    }

    /**
     * @brief 获取客户端引用
     * @return TCP总线客户端引用
     */
    zexuan::bus::TcpBusClient& getClient() { return client_; }

    /**
     * @brief 获取客户端引用（const版本）
     * @return TCP总线客户端引用
     */
    const zexuan::bus::TcpBusClient& getClient() const { return client_; }

private:
    zexuan::bus::TcpBusClient& client_;  ///< TCP总线客户端引用
};

} // namespace devices

#endif // DEVICES_BASE_MESSAGE_PROCESSOR_HPP

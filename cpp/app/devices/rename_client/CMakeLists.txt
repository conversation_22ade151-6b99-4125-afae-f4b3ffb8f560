# 多线程重命名客户端CMakeLists.txt

# 设置源文件
set(SOURCES
    source/main.cpp
)

# 设置头文件
set(HEADERS
    include/rename_processor.hpp
    include/rename_client_app.hpp
)

# 创建可执行文件
add_executable(rename_client ${SOURCES} ${HEADERS})

# 链接库
target_link_libraries(rename_client
    PRIVATE
        zexuan_base
        zexuan_net
        zexuan_bus
        zexuan_utils
        nlohmann_json::nlohmann_json
        spdlog::spdlog
)

# 设置C++标准
target_compile_features(rename_client PRIVATE cxx_std_17)

# 包含头文件目录
target_include_directories(rename_client
    PRIVATE
        ${CMAKE_SOURCE_DIR}/cpp/include
        ${CMAKE_CURRENT_SOURCE_DIR}/include
        ${CMAKE_SOURCE_DIR}/cpp/app/devices/include
)

# 设置输出目录
set_target_properties(rename_client PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin/devices/
)

# 安装可执行文件
install(TARGETS rename_client
    RUNTIME DESTINATION bin
    COMPONENT applications
)

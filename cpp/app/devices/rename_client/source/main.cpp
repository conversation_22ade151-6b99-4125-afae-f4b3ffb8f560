/**
 * @file main.cpp
 * @brief 多线程重命名客户端主程序
 * <AUTHOR>
 * @date 2025-08-26
 */

#include <spdlog/spdlog.h>
#include <iostream>
#include <memory>
#include <string>
#include <atomic>
#include <fstream>
#include <nlohmann/json.hpp>

#include "zexuan/base/logger_manager.hpp"
#include "zexuan/net/event_loop.hpp"
#include "../include/rename_client_app.hpp"

using namespace zexuan::net;
using namespace zexuan::base;
using namespace rename_client;

// 全局变量
std::atomic<bool> g_running{true};
std::shared_ptr<EventLoop> g_event_loop;
std::unique_ptr<RenameClientApp> g_app;

/**
 * @brief 配置结构体
 */
struct RenameClientConfig {
    std::string client_name = "RenameClient";
    int client_id = 10;
    size_t thread_pool_size = 32;
};

/**
 * @brief 从配置文件加载配置
 */
bool loadConfig(const std::string& config_path, RenameClientConfig& config) {
    try {
        std::ifstream config_file(config_path);
        if (!config_file.is_open()) {
            spdlog::warn("Cannot open config file: {}, using default config", config_path);
            return true; // 使用默认配置
        }

        nlohmann::json config_json;
        config_file >> config_json;

        // 查找rename_client配置
        if (config_json.contains("devices") && config_json["devices"].contains("rename_client")) {
            auto client_config = config_json["devices"]["rename_client"];

            if (client_config.contains("clientid") && client_config["clientid"].is_number_integer()) {
                config.client_id = client_config["clientid"].get<int>();
            }

            if (client_config.contains("thread_pool_size") && client_config["thread_pool_size"].is_number_integer()) {
                config.thread_pool_size = client_config["thread_pool_size"].get<size_t>();
            }
        }

        return true;
    } catch (const std::exception& e) {
        spdlog::error("Failed to load config from {}: {}", config_path, e.what());
        return false;
    }
}

/**
 * @brief 打印使用说明
 */
void printUsage(const char* program_name) {
    std::cout << "Usage: " << program_name << " [config_path]" << std::endl;
    std::cout << "  config_path     : Config file path (default: ./config/config.json)" << std::endl;
    std::cout << std::endl;
    std::cout << "Configuration is loaded from config.json:" << std::endl;
    std::cout << "  - bus.client.server_host: Server address (used by TcpBusClient)" << std::endl;
    std::cout << "  - bus.client.server_port: Server port (used by TcpBusClient)" << std::endl;
    std::cout << "  - devices.rename_client.clientid: Client ID" << std::endl;
    std::cout << "  - devices.rename_client.thread_pool_size: Thread pool size" << std::endl;
    std::cout << std::endl;
    std::cout << "Example:" << std::endl;
    std::cout << "  " << program_name << " ./config/config.json" << std::endl;
}

int main(int argc, char* argv[]) {
    // 解析命令行参数
    std::string config_path = "./config/config.json";

    // 检查是否请求帮助
    if (argc > 1 && (std::string(argv[1]) == "-h" || std::string(argv[1]) == "--help")) {
        printUsage(argv[0]);
        return 0;
    }

    // 解析配置文件路径
    if (argc > 1) {
        config_path = argv[1];
    }

    // 加载配置
    RenameClientConfig config;
    if (!loadConfig(config_path, config)) {
        std::cerr << "Failed to load configuration from: " << config_path << std::endl;
        return -1;
    }

    // 初始化日志系统
    if (!LoggerManager::initialize("rename_client.log", config_path)) {
        std::cerr << "Failed to initialize logger" << std::endl;
        return -1;
    }

    spdlog::info("=== Multi-threaded Rename Client Starting ===");
    spdlog::info("Client: {} (ID: {})", config.client_name, config.client_id);
    spdlog::info("Thread Pool Size: {}", config.thread_pool_size);
    spdlog::info("Config Path: {}", config_path);
    spdlog::info("Server connection will be configured from bus.client section");

    try {
        // 创建事件循环
        g_event_loop = std::make_shared<EventLoop>();

        // 注册优雅关闭信号
        g_event_loop->registerShutdownSignals([&](int signal) {
            spdlog::info("Received signal {}, shutting down gracefully...", signal);
            g_running.store(false);
            g_event_loop->quit();
        });

        // 创建重命名客户端应用程序实例
        g_app = std::make_unique<RenameClientApp>(
            *g_event_loop,
            config_path,
            config.client_name,
            config.client_id,
            config.thread_pool_size
        );

        // 启动应用程序
        if (!g_app->start()) {
            spdlog::error("Failed to start Rename Client App");
            return -1;
        }

        spdlog::info("Rename Client App started successfully");
        spdlog::info("Ready to process file rename requests...");

        // 运行事件循环（阻塞直到quit()被调用）
        g_event_loop->loop();

        spdlog::info("Event loop stopped");

        // 确保完全清理应用程序
        if (g_app) {
            spdlog::info("Stopping and cleaning up application...");
            g_app->stop();
            g_app.reset();
            spdlog::info("Application cleanup completed");
        }

        // 清理事件循环
        g_event_loop.reset();

    } catch (const std::exception& e) {
        spdlog::error("Exception in main: {}", e.what());
        return -1;
    }

    spdlog::info("=== Multi-threaded Rename Client Shutdown Completed ===");
    return 0;
}

# 设备基类使用说明

本目录包含了设备相关的公共基类，用于简化TCP总线客户端应用程序的开发。

## 基类结构

### 1. BaseEventReporter (基础事件上报器)
位置：`include/base_event_reporter.hpp`

**功能**：
- 提供定时事件上报的基础框架
- 管理上报器的启动、停止和运行状态
- 提供事件消息创建和发送的通用方法

**使用方法**：
```cpp
class MyEventReporter : public devices::BaseEventReporter {
public:
    MyEventReporter(zexuan::bus::TcpBusClient& client, 
                    zexuan::net::EventLoop& event_loop,
                    double interval_seconds = 3.0)
        : BaseEventReporter(client, event_loop, interval_seconds) {}

protected:
    void generateEvent() override {
        // 实现具体的事件生成逻辑
        auto event_msg = createBaseEventMessage(1, "My custom event");
        // 添加自定义数据
        sendEventMessage(event_msg);
    }
};
```

### 2. BaseMessageProcessor (基础消息处理器)
位置：`include/base_message_processor.hpp`

**功能**：
- 定义消息处理的通用接口
- 提供响应消息创建和发送的通用方法
- 管理TCP总线客户端的访问

**使用方法**：
```cpp
class MyMessageProcessor : public devices::BaseMessageProcessor {
public:
    explicit MyMessageProcessor(zexuan::bus::TcpBusClient& client) 
        : BaseMessageProcessor(client) {}

    bool processMessage(const zexuan::base::CommonMessage& original_message,
                       const zexuan::base::Message& input_msg) override {
        // 实现具体的消息处理逻辑
        auto response_msg = createResponse(input_msg, 0x07, "Response content");
        
        std::vector<uint8_t> response_data;
        response_msg.serialize(response_data);
        
        auto response = createBaseResponse(original_message);
        response.data = response_data;
        
        return sendResponse(response);
    }

    bool canProcess(uint8_t message_type) const override {
        return message_type == 1; // 处理类型1的消息
    }

    std::string getName() const override {
        return "MyMessageProcessor";
    }
};
```

### 3. BaseClientApp (基础客户端应用程序)
位置：`include/base_client_app.hpp`

**功能**：
- 提供完整的TCP总线客户端应用程序框架
- 管理连接、订阅、消息处理器和事件上报器
- 处理各种类型的消息回调

**使用方法**：
```cpp
class MyClientApp : public devices::BaseClientApp {
public:
    MyClientApp(zexuan::net::EventLoop& event_loop,
                const std::string& server_host,
                uint16_t server_port,
                const std::string& client_name,
                int client_id)
        : BaseClientApp(event_loop, server_host, server_port, client_name, client_id) {}

protected:
    bool initialize() override {
        // 添加消息处理器
        addProcessor(std::make_unique<MyMessageProcessor>(getClient()));
        
        // 设置事件上报器
        setEventReporter(std::make_unique<MyEventReporter>(getClient(), getEventLoop()));
        
        return true;
    }

    std::vector<int> getSubscribedMessageTypes() const override {
        return {1, 2, 3}; // 订阅消息类型1, 2, 3
    }

    std::vector<int> getSubscribedEventTypes() const override {
        return {1}; // 订阅事件类型1
    }
};
```

## 示例实现

`tcp_bus_client` 目录展示了如何使用这些基类：

1. **EventReporter** - 继承自 `BaseEventReporter`，实现时间戳事件上报
2. **MessageProcessor** - 继承自 `BaseMessageProcessor`，提供IEC103特定的方法
3. **Type1Processor** 和 **Type2Processor** - 具体的消息处理器实现
4. **TcpBusClientApp** - 继承自 `BaseClientApp`，实现完整的TCP总线客户端

## 优势

1. **代码复用**：公共功能在基类中实现，避免重复代码
2. **一致性**：所有设备应用程序使用相同的基础架构
3. **可扩展性**：通过继承和重写虚函数轻松扩展功能
4. **维护性**：基类的改进会自动应用到所有子类

## 编译

确保在CMakeLists.txt中包含基类头文件路径：

```cmake
target_include_directories(your_target PRIVATE
    ${CMAKE_CURRENT_SOURCE_DIR}/../include
)
```

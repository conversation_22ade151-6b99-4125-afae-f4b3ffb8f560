#include "zexuan/net/event_loop_thread.hpp"

#include <spdlog/spdlog.h>

#include <cassert>

#include "zexuan/net/event_loop.hpp"

using namespace zexuan;
using namespace zexuan::net;

EventLoopThread::EventLoopThread(const ThreadInitCallback& cb, const std::string& name)
    : loop_(NULL), exiting_(false), mutex_(), cond_(), callback_(cb), name_(name) {}

EventLoopThread::~EventLoopThread() {
  exiting_ = true;
  if (loop_ != NULL)  // not 100% race-free, eg. threadFunc could be running callback_.
  {
    // still a tiny chance to call destructed object, if thread<PERSON><PERSON><PERSON> exits just now.
    // but when EventLoopThread destructs, usually programming is exiting anyway.
    spdlog::debug("EventLoopThread [{}] stopping", name_);
    loop_->quit();
    thread_.join();
    spdlog::debug("EventLoopThread [{}] stopped", name_);
  }
}

EventLoop* EventLoopThread::startLoop() {
  assert(!thread_.joinable());
  spdlog::debug("EventLoopThread [{}] starting", name_);
  thread_ = std::thread(&EventLoopThread::threadFunc, this);

  EventLoop* loop = NULL;
  {
    std::unique_lock<std::mutex> lock(mutex_);
    while (loop_ == NULL) {
      cond_.wait(lock);
    }
    loop = loop_;
  }

  spdlog::debug("EventLoopThread [{}] started successfully", name_);
  return loop;
}

void EventLoopThread::threadFunc() {
  spdlog::trace("EventLoopThread [{}] thread function started", name_);
  EventLoop loop;

  if (callback_) {
    callback_(&loop);
  }

  {
    std::lock_guard<std::mutex> lock(mutex_);
    loop_ = &loop;
    cond_.notify_one();
  }

  spdlog::trace("EventLoopThread [{}] entering event loop", name_);
  loop.loop();
  spdlog::trace("EventLoopThread [{}] exited event loop", name_);

  // assert(exiting_);
  std::lock_guard<std::mutex> lock(mutex_);
  loop_ = NULL;
}

/**
 * @file message_router.cpp
 * @brief 消息路由器实现
 * <AUTHOR>
 * @date 2025-08-24
 */

#include "zexuan/bus/message_router.hpp"
#include <spdlog/spdlog.h>
#include <algorithm>

namespace zexuan {
namespace bus {

MessageRouter::MessageRouter(std::shared_ptr<SubscriptionManager> subscription_manager)
    : subscription_manager_(std::move(subscription_manager)) {
    if (!subscription_manager_) {
        throw std::invalid_argument("SubscriptionManager cannot be null");
    }
}

size_t MessageRouter::routeCommonMessage(const TcpConnectionPtr& sender, const zexuan::base::CommonMessage& message) {
    if (!subscription_manager_) {
        routing_errors_++;
        return 0;
    }
    
    // 统一的点对点路由策略：如果有target_id，直接路由
    if (message.target_id != zexuan::base::INVALID_ID) {
        bool success = routePointToPointMessage(sender, message);
        if (success) {
            common_messages_routed_++;
            total_messages_routed_++;
            logRouting("CommonMessage (P2P via target_id)", sender, 1);
            return 1;
        } else {
            no_subscribers_count_++;
            logRouting("CommonMessage (P2P, target not found)", sender, 0);
            return 0;
        }
    }
    
    // 没有target_id的消息按广播处理
    auto subscribers = subscription_manager_->getSubscribersForMessageType(static_cast<int>(message.type));
    
    if (subscribers.empty()) {
        no_subscribers_count_++;
        logRouting("CommonMessage (no subscribers)", sender, 0);
        return 0;
    }

    // 序列化消息
    std::string json_message = zexuan::utils::MessageSerializer::serializeCommonMessage(message);
    if (json_message.empty()) {
        spdlog::error("MessageRouter: Failed to serialize CommonMessage");
        serialization_errors_++;
        return 0;
    }

    // 发送给订阅者
    size_t sent_count = sendMessageToConnections(subscribers, json_message, true, sender);

    common_messages_routed_++;
    total_messages_routed_++;
    logRouting("CommonMessage (broadcast)", sender, sent_count);
    
    return sent_count;
}

size_t MessageRouter::routeEventMessage(const TcpConnectionPtr& sender, const zexuan::base::EventMessage& message) {
    if (!subscription_manager_) {
        routing_errors_++;
        return 0;
    }

    // 获取订阅者
    auto subscribers = subscription_manager_->getSubscribersForEventType(message.event_type);

    if (subscribers.empty()) {
        no_subscribers_count_++;
        logRouting("EventMessage (no subscribers)", sender, 0);
        return 0;
    }

    // 序列化消息
    std::string json_message = zexuan::utils::MessageSerializer::serializeEventMessage(message);
    if (json_message.empty()) {
        spdlog::error("MessageRouter: Failed to serialize EventMessage");
        serialization_errors_++;
        return 0;
    }

    // 发送给订阅者
    size_t sent_count = sendMessageToConnections(subscribers, json_message, true, sender);

    event_messages_routed_++;
    total_messages_routed_++;
    logRouting("EventMessage", sender, sent_count);
    
    return sent_count;
}

bool MessageRouter::sendControlMessage(const TcpConnectionPtr& conn, const zexuan::base::ControlMessage& control_message) {
    if (!conn || !conn->connected()) {
        return false;
    }

    std::string json_message = zexuan::utils::MessageSerializer::serializeControlMessage(control_message);
    if (json_message.empty()) {
        spdlog::error("MessageRouter: Failed to serialize ControlMessage");
        serialization_errors_++;
        return false;
    }

    bool success = sendMessageToConnection(conn, json_message);
    if (success) {
        control_messages_sent_++;
    }
    
    return success;
}

size_t MessageRouter::broadcastControlMessage(const zexuan::base::ControlMessage& control_message) {
    if (!subscription_manager_) {
        return 0;
    }

    auto all_clients = subscription_manager_->getAllClients();
    if (all_clients.empty()) {
        return 0;
    }

    std::string json_message = zexuan::utils::MessageSerializer::serializeControlMessage(control_message);
    if (json_message.empty()) {
        spdlog::error("MessageRouter: Failed to serialize ControlMessage for broadcast");
        serialization_errors_++;
        return 0;
    }

    size_t sent_count = sendMessageToConnections(all_clients, json_message);
    control_messages_sent_ += sent_count;
    
    return sent_count;
}

bool MessageRouter::routePointToPointMessage(const TcpConnectionPtr& sender, const zexuan::base::CommonMessage& message) {
    // 根据target_id查找目标连接
    auto target_conn = findConnectionByTargetId(message.target_id);
    if (!target_conn) {
        spdlog::warn("MessageRouter: Target client not found for target_id: {}", message.target_id);
        return false;
    }
    
    // 序列化消息
    std::string json_message = zexuan::utils::MessageSerializer::serializeCommonMessage(message);
    if (json_message.empty()) {
        spdlog::error("MessageRouter: Failed to serialize CommonMessage for P2P");
        serialization_errors_++;
        return false;
    }
    
    // 发送消息
    return sendMessageToConnection(target_conn, json_message);
}

RoutingStatistics MessageRouter::getStatistics() const {
    RoutingStatistics stats;
    stats.total_messages_routed = total_messages_routed_.load();
    stats.common_messages_routed = common_messages_routed_.load();
    stats.event_messages_routed = event_messages_routed_.load();
    stats.control_messages_sent = control_messages_sent_.load();
    stats.routing_errors = routing_errors_.load();
    stats.serialization_errors = serialization_errors_.load();
    stats.no_subscribers_count = no_subscribers_count_.load();
    return stats;
}

void MessageRouter::resetStatistics() {
    total_messages_routed_ = 0;
    common_messages_routed_ = 0;
    event_messages_routed_ = 0;
    control_messages_sent_ = 0;
    routing_errors_ = 0;
    serialization_errors_ = 0;
    no_subscribers_count_ = 0;
}

bool MessageRouter::sendMessageToConnection(const TcpConnectionPtr& conn, const std::string& json_message) {
    if (!conn || !conn->connected()) {
        spdlog::warn("Cannot send message: connection is null or not connected");
        return false;
    }

    try {
        if (verbose_logging_) {
            spdlog::debug("Sending {} bytes to {}", json_message.size(), conn->peerAddress().toIpPort());
            spdlog::trace("Message content: {}", json_message.substr(0, 200));
        }

        // 使用协议打包消息
        auto packed_message = zexuan::utils::MessageProtocol::packMessage(json_message);
        if (packed_message.empty()) {
            spdlog::error("MessageRouter: Failed to pack message for connection: {}", conn->name());
            return false;
        }

        // 发送打包后的消息
        conn->send(packed_message.data(), packed_message.size());

        if (verbose_logging_) {
            spdlog::trace("Successfully sent {} bytes to {}", packed_message.size(), conn->peerAddress().toIpPort());
        }
        return true;

    } catch (const std::exception& e) {
        spdlog::error("MessageRouter: Failed to send message to {}: {}", conn->peerAddress().toIpPort(), e.what());
        return false;
    }
}

size_t MessageRouter::sendMessageToConnections(const std::vector<TcpConnectionPtr>& connections,
                                              const std::string& json_message,
                                              bool exclude_sender,
                                              const TcpConnectionPtr& sender) {
    if (connections.empty()) {
        return 0;
    }

    // 预先打包消息以提高效率
    auto packed_message = zexuan::utils::MessageProtocol::packMessage(json_message);
    if (packed_message.empty()) {
        spdlog::error("MessageRouter: Failed to pack message for broadcast");
        return 0;
    }

    size_t sent_count = 0;
    size_t failed_count = 0;

    for (const auto& conn : connections) {
        if (!conn || !conn->connected()) {
            continue;
        }

        // 排除发送者
        if (exclude_sender && conn == sender) {
            continue;
        }

        try {
            conn->send(packed_message.data(), packed_message.size());
            sent_count++;
        } catch (const std::exception& e) {
            spdlog::error("MessageRouter: Failed to send message to {}: {}", conn->peerAddress().toIpPort(), e.what());
            failed_count++;
        }
    }

    if (verbose_logging_ && failed_count > 0) {
        spdlog::warn("MessageRouter: Broadcast completed: {} sent, {} failed", sent_count, failed_count);
    }

    return sent_count;
}

MessageRouter::TcpConnectionPtr MessageRouter::findConnectionByTargetId(zexuan::base::ObjectId target_id) {
    if (!subscription_manager_) {
        return nullptr;
    }
    
    // 使用订阅管理器的客户端ID映射查找连接
    return subscription_manager_->findConnectionByClientId(target_id);
}

void MessageRouter::logRouting(const std::string& message_type, const TcpConnectionPtr& sender, size_t subscriber_count) {
    if (!verbose_logging_) {
        return;
    }
    
    std::string sender_info = sender ? sender->peerAddress().toIpPort() : "system";
    spdlog::debug("MessageRouter: Routed {} from {} to {} subscribers", message_type, sender_info, subscriber_count);
}

} // namespace bus
} // namespace zexuan

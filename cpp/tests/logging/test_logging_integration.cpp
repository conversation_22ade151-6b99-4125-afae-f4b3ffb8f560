/**
 * @file test_logging_integration.cpp
 * @brief 日志系统集成测试
 * <AUTHOR>
 * @date 2025-08-26
 */

#include <gtest/gtest.h>
#include <spdlog/spdlog.h>
#include <fstream>
#include <filesystem>
#include <memory>

#include "zexuan/base/logger_manager.hpp"
#include "zexuan/base/lifecycle_component.hpp"
#include "zexuan/net/event_loop.hpp"
#include "zexuan/bus/tcp_bus_server.hpp"
#include "zexuan/bus/tcp_bus_client.hpp"

using namespace zexuan::base;
using namespace zexuan::net;
using namespace zexuan::bus;

class LoggingIntegrationTest : public ::testing::Test {
protected:
    void SetUp() override {
        // 创建测试目录
        test_dir_ = "integration_test_logs";
        std::filesystem::create_directories(test_dir_);
        
        // 创建测试配置文件
        config_path_ = test_dir_ + "/integration_config.json";
        createTestConfig();
        
        // 初始化日志系统
        ASSERT_TRUE(LoggerManager::initialize("integration_test.log", config_path_));
    }
    
    void TearDown() override {
        // 清理测试文件
        std::filesystem::remove_all(test_dir_);
    }
    
    void createTestConfig() {
        std::ofstream config_file(config_path_);
        config_file << R"({
            "spdlog": {
                "level": "debug",
                "pattern": "[%Y-%m-%d %H:%M:%S.%e] [%^%l%$] %v",
                "max_file_size": 10485760,
                "max_files": 3,
                "flush_on_debug": true,
                "log_directory": ")" << test_dir_ << R"("
            },
            "bus": {
                "server": {
                    "host": "127.0.0.1",
                    "port": 18080,
                    "thread_pool_size": 2,
                    "verbose_logging": true,
                    "max_connections": 100
                },
                "client": {
                    "server_host": "127.0.0.1",
                    "server_port": 18080
                }
            }
        })";
        config_file.close();
    }
    
    std::string readLogFile(const std::string& filename) {
        std::ifstream file(test_dir_ + "/" + filename);
        if (!file.is_open()) {
            return "";
        }
        
        std::string content;
        std::string line;
        while (std::getline(file, line)) {
            content += line + "\n";
        }
        return content;
    }
    
    std::string test_dir_;
    std::string config_path_;
};

// 测试组件生命周期日志
TEST_F(LoggingIntegrationTest, ComponentLifecycleLogging) {
    // 创建一个简单的测试组件
    class TestComponent : public LifecycleComponentBase {
    public:
        TestComponent() : LifecycleComponentBase("TestComponent") {}
        
        bool Start() override {
            if (!TransitionTo(ComponentState::STARTING)) {
                return false;
            }
            
            spdlog::info("{}: Starting component", GetComponentName());
            
            // 模拟配置加载
            spdlog::debug("{}: Loading configuration", GetComponentName());
            
            // 模拟资源初始化
            spdlog::debug("{}: Initializing resources", GetComponentName());
            
            if (!TransitionTo(ComponentState::RUNNING)) {
                return false;
            }
            
            spdlog::info("{}: Component started successfully", GetComponentName());
            return true;
        }
        
        bool Stop() override {
            if (!TransitionTo(ComponentState::STOPPING)) {
                return false;
            }
            
            spdlog::info("{}: Stopping component", GetComponentName());
            
            // 模拟资源清理
            spdlog::debug("{}: Cleaning up resources", GetComponentName());
            
            if (!TransitionTo(ComponentState::STOPPED)) {
                return false;
            }
            
            spdlog::info("{}: Component stopped successfully", GetComponentName());
            return true;
        }
    };
    
    auto component = std::make_unique<TestComponent>();
    
    // 测试启动
    EXPECT_TRUE(component->Start());
    EXPECT_EQ(component->GetState(), ComponentState::RUNNING);
    
    // 测试停止
    EXPECT_TRUE(component->Stop());
    EXPECT_EQ(component->GetState(), ComponentState::STOPPED);
    
    // 强制刷新日志
    spdlog::default_logger()->flush();
    
    // 验证日志内容
    std::string log_content = readLogFile("integration_test.log");
    EXPECT_TRUE(log_content.find("TestComponent: Starting component") != std::string::npos);
    EXPECT_TRUE(log_content.find("TestComponent: Loading configuration") != std::string::npos);
    EXPECT_TRUE(log_content.find("TestComponent: Initializing resources") != std::string::npos);
    EXPECT_TRUE(log_content.find("TestComponent: Component started successfully") != std::string::npos);
    EXPECT_TRUE(log_content.find("TestComponent: Stopping component") != std::string::npos);
    EXPECT_TRUE(log_content.find("TestComponent: Cleaning up resources") != std::string::npos);
    EXPECT_TRUE(log_content.find("TestComponent: Component stopped successfully") != std::string::npos);
}

// 测试网络组件日志
TEST_F(LoggingIntegrationTest, NetworkComponentLogging) {
    auto event_loop = std::make_unique<EventLoop>();
    
    // 创建TCP服务器
    auto server = std::make_unique<TcpBusServer>(event_loop.get(), config_path_);
    
    // 启动服务器
    EXPECT_TRUE(server->Start());
    
    // 等待一小段时间让服务器完全启动
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    
    // 停止服务器
    EXPECT_TRUE(server->Stop());
    
    // 强制刷新日志
    spdlog::default_logger()->flush();
    
    // 验证日志内容
    std::string log_content = readLogFile("integration_test.log");
    EXPECT_TRUE(log_content.find("TcpBusServer: Starting component") != std::string::npos);
    EXPECT_TRUE(log_content.find("TcpBusServer: Component started successfully") != std::string::npos);
    EXPECT_TRUE(log_content.find("TcpBusServer: Stopping component") != std::string::npos);
    EXPECT_TRUE(log_content.find("TcpBusServer: Component stopped successfully") != std::string::npos);
}

// 测试错误处理日志
TEST_F(LoggingIntegrationTest, ErrorHandlingLogging) {
    // 创建一个会失败的组件
    class FailingComponent : public LifecycleComponentBase {
    public:
        FailingComponent() : LifecycleComponentBase("FailingComponent") {}
        
        bool Start() override {
            if (!TransitionTo(ComponentState::STARTING)) {
                return false;
            }
            
            spdlog::info("{}: Starting component", GetComponentName());
            
            // 模拟启动失败
            spdlog::error("{}: Failed to initialize critical resource", GetComponentName());
            
            TransitionTo(ComponentState::ERROR);
            return false;
        }
        
        bool Stop() override {
            spdlog::info("{}: Stopping component", GetComponentName());
            TransitionTo(ComponentState::STOPPED);
            return true;
        }
    };
    
    auto component = std::make_unique<FailingComponent>();
    
    // 测试启动失败
    EXPECT_FALSE(component->Start());
    EXPECT_EQ(component->GetState(), ComponentState::ERROR);
    
    // 强制刷新日志
    spdlog::default_logger()->flush();
    
    // 验证错误日志
    std::string log_content = readLogFile("integration_test.log");
    EXPECT_TRUE(log_content.find("FailingComponent: Starting component") != std::string::npos);
    EXPECT_TRUE(log_content.find("FailingComponent: Failed to initialize critical resource") != std::string::npos);
}

// 测试多线程日志安全性
TEST_F(LoggingIntegrationTest, MultithreadedLogging) {
    const int num_threads = 4;
    const int messages_per_thread = 100;
    
    std::vector<std::thread> threads;
    
    // 启动多个线程同时写日志
    for (int i = 0; i < num_threads; ++i) {
        threads.emplace_back([i, messages_per_thread]() {
            for (int j = 0; j < messages_per_thread; ++j) {
                spdlog::info("Thread{}: Message {}", i, j);
                spdlog::debug("Thread{}: Debug message {}", i, j);
            }
        });
    }
    
    // 等待所有线程完成
    for (auto& thread : threads) {
        thread.join();
    }
    
    // 强制刷新日志
    spdlog::default_logger()->flush();
    
    // 验证日志完整性
    std::string log_content = readLogFile("integration_test.log");
    
    // 计算每个线程的消息数量
    for (int i = 0; i < num_threads; ++i) {
        size_t count = 0;
        size_t pos = 0;
        std::string pattern = "Thread" + std::to_string(i) + ":";
        
        while ((pos = log_content.find(pattern, pos)) != std::string::npos) {
            count++;
            pos++;
        }
        
        // 每个线程应该有 messages_per_thread * 2 条消息（info + debug）
        EXPECT_EQ(count, messages_per_thread * 2);
    }
}

int main(int argc, char** argv) {
    ::testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}

/**
 * @file test_logging_system.cpp
 * @brief 日志系统测试
 * <AUTHOR>
 * @date 2025-08-26
 */

#include <gtest/gtest.h>
#include <spdlog/spdlog.h>
#include <fstream>
#include <filesystem>
#include <thread>
#include <chrono>

#include "zexuan/base/logger_manager.hpp"
#include "zexuan/utils/logging_performance.hpp"

using namespace zexuan::base;
using namespace zexuan::utils;

class LoggingSystemTest : public ::testing::Test {
protected:
    void SetUp() override {
        // 创建测试目录
        test_dir_ = "test_logs";
        std::filesystem::create_directories(test_dir_);
        
        // 创建测试配置文件
        config_path_ = test_dir_ + "/test_config.json";
        createTestConfig();
    }
    
    void TearDown() override {
        // 清理测试文件
        std::filesystem::remove_all(test_dir_);
    }
    
    void createTestConfig() {
        std::ofstream config_file(config_path_);
        config_file << R"({
            "spdlog": {
                "level": "trace",
                "pattern": "[%Y-%m-%d %H:%M:%S.%e] [%^%l%$] %v",
                "max_file_size": 1048576,
                "max_files": 3,
                "flush_on_debug": true,
                "log_directory": ")" << test_dir_ << R"("
            }
        })";
        config_file.close();
    }
    
    std::string readLogFile(const std::string& filename) {
        std::ifstream file(test_dir_ + "/" + filename);
        if (!file.is_open()) {
            return "";
        }
        
        std::string content;
        std::string line;
        while (std::getline(file, line)) {
            content += line + "\n";
        }
        return content;
    }
    
    std::string test_dir_;
    std::string config_path_;
};

// 测试日志管理器初始化
TEST_F(LoggingSystemTest, LoggerManagerInitialization) {
    EXPECT_TRUE(LoggerManager::initialize("test.log", config_path_));
    
    // 测试基本日志记录
    spdlog::info("Test info message");
    spdlog::debug("Test debug message");
    spdlog::trace("Test trace message");
    spdlog::error("Test error message");
    
    // 强制刷新日志
    spdlog::default_logger()->flush();
    
    // 检查日志文件是否创建
    EXPECT_TRUE(std::filesystem::exists(test_dir_ + "/test.log"));
    
    // 检查日志内容
    std::string log_content = readLogFile("test.log");
    EXPECT_TRUE(log_content.find("Test info message") != std::string::npos);
    EXPECT_TRUE(log_content.find("Test debug message") != std::string::npos);
    EXPECT_TRUE(log_content.find("Test trace message") != std::string::npos);
    EXPECT_TRUE(log_content.find("Test error message") != std::string::npos);
}

// 测试日志级别过滤
TEST_F(LoggingSystemTest, LogLevelFiltering) {
    // 创建只记录info及以上级别的配置
    std::ofstream config_file(config_path_);
    config_file << R"({
        "spdlog": {
            "level": "info",
            "pattern": "[%Y-%m-%d %H:%M:%S.%e] [%^%l%$] %v",
            "max_file_size": 1048576,
            "max_files": 3,
            "flush_on_debug": true,
            "log_directory": ")" << test_dir_ << R"("
        }
    })";
    config_file.close();
    
    EXPECT_TRUE(LoggerManager::initialize("level_test.log", config_path_));
    
    spdlog::trace("This should not appear");
    spdlog::debug("This should not appear");
    spdlog::info("This should appear");
    spdlog::warn("This should appear");
    spdlog::error("This should appear");
    
    spdlog::default_logger()->flush();
    
    std::string log_content = readLogFile("level_test.log");
    EXPECT_TRUE(log_content.find("This should not appear") == std::string::npos);
    EXPECT_TRUE(log_content.find("This should appear") != std::string::npos);
}

// 测试组件名称格式
TEST_F(LoggingSystemTest, ComponentNameFormat) {
    EXPECT_TRUE(LoggerManager::initialize("component_test.log", config_path_));
    
    // 模拟组件日志
    std::string component_name = "TestComponent";
    spdlog::info("{}: Component started successfully", component_name);
    spdlog::error("{}: Failed to initialize", component_name);
    spdlog::debug("{}: Processing message type {}", component_name, 42);
    
    spdlog::default_logger()->flush();
    
    std::string log_content = readLogFile("component_test.log");
    EXPECT_TRUE(log_content.find("TestComponent: Component started successfully") != std::string::npos);
    EXPECT_TRUE(log_content.find("TestComponent: Failed to initialize") != std::string::npos);
    EXPECT_TRUE(log_content.find("TestComponent: Processing message type 42") != std::string::npos);
}

// 测试网络连接日志格式
TEST_F(LoggingSystemTest, NetworkConnectionFormat) {
    EXPECT_TRUE(LoggerManager::initialize("network_test.log", config_path_));
    
    // 模拟网络连接日志
    std::string local_addr = "127.0.0.1:8080";
    std::string remote_addr = "127.0.0.1:12345";
    
    spdlog::info("Connection established: {} -> {}", local_addr, remote_addr);
    spdlog::info("Connection closed: {}", remote_addr);
    spdlog::debug("Received {} bytes from {}", 1024, remote_addr);
    
    spdlog::default_logger()->flush();
    
    std::string log_content = readLogFile("network_test.log");
    EXPECT_TRUE(log_content.find("Connection established: 127.0.0.1:8080 -> 127.0.0.1:12345") != std::string::npos);
    EXPECT_TRUE(log_content.find("Connection closed: 127.0.0.1:12345") != std::string::npos);
    EXPECT_TRUE(log_content.find("Received 1024 bytes from 127.0.0.1:12345") != std::string::npos);
}

// 测试性能优化功能
TEST_F(LoggingSystemTest, PerformanceOptimization) {
    EXPECT_TRUE(LoggerManager::initialize("perf_test.log", config_path_));
    
    // 测试条件日志
    LoggingPerformance::logIf(true, spdlog::level::info, "This should appear");
    LoggingPerformance::logIf(false, spdlog::level::info, "This should not appear");
    
    // 测试采样日志
    for (int i = 0; i < 10; ++i) {
        LoggingPerformance::logWithSampling(5, spdlog::level::debug, "Sample message {}", i);
    }
    
    spdlog::default_logger()->flush();
    
    std::string log_content = readLogFile("perf_test.log");
    EXPECT_TRUE(log_content.find("This should appear") != std::string::npos);
    EXPECT_TRUE(log_content.find("This should not appear") == std::string::npos);
    
    // 采样日志应该只出现2次（每5次记录一次，共10次）
    size_t count = 0;
    size_t pos = 0;
    while ((pos = log_content.find("Sample message", pos)) != std::string::npos) {
        count++;
        pos++;
    }
    EXPECT_EQ(count, 2);
}

// 测试频率限制日志
TEST_F(LoggingSystemTest, RateLimitedLogging) {
    EXPECT_TRUE(LoggerManager::initialize("rate_limit_test.log", config_path_));
    
    // 快速连续记录多条日志，应该被频率限制
    for (int i = 0; i < 5; ++i) {
        LoggingPerformance::logWithRateLimit(1000, spdlog::level::info, "Rate limited message {}", i);
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }
    
    spdlog::default_logger()->flush();
    
    std::string log_content = readLogFile("rate_limit_test.log");
    
    // 应该只有第一条消息被记录（1000ms间隔，但只等待了100ms）
    size_t count = 0;
    size_t pos = 0;
    while ((pos = log_content.find("Rate limited message", pos)) != std::string::npos) {
        count++;
        pos++;
    }
    EXPECT_LE(count, 2); // 最多2条消息（考虑到时间精度）
}

// 测试错误处理
TEST_F(LoggingSystemTest, ErrorHandling) {
    // 测试无效配置文件
    EXPECT_FALSE(LoggerManager::initialize("error_test.log", "non_existent_config.json"));
    
    // 测试无效JSON配置
    std::string invalid_config_path = test_dir_ + "/invalid_config.json";
    std::ofstream invalid_config(invalid_config_path);
    invalid_config << "{ invalid json }";
    invalid_config.close();
    
    // 应该回退到控制台日志
    EXPECT_TRUE(LoggerManager::initialize("error_test.log", invalid_config_path));
}

int main(int argc, char** argv) {
    ::testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}

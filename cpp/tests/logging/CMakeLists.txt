# 日志系统测试

# 查找Google Test
find_package(GTest REQUIRED)

# 创建测试可执行文件
add_executable(test_logging_system
    test_logging_system.cpp
)

# 链接库
target_link_libraries(test_logging_system
    PRIVATE
    zexuan_base
    zexuan_utils
    GTest::gtest
    GTest::gtest_main
    spdlog::spdlog
    nlohmann_json::nlohmann_json
)

# 包含目录
target_include_directories(test_logging_system
    PRIVATE
    ${CMAKE_SOURCE_DIR}/include
    ${CMAKE_SOURCE_DIR}/third_party/spdlog/include
    ${CMAKE_SOURCE_DIR}/third_party/json/include
)

# 添加测试
add_test(NAME LoggingSystemTest COMMAND test_logging_system)

# 设置测试属性
set_tests_properties(LoggingSystemTest PROPERTIES
    TIMEOUT 30
    WORKING_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}
)

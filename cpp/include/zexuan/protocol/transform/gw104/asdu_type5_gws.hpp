#pragma once

#include "zexuan/protocol/transform/asdu_base.hpp"

namespace zexuan {
  namespace protocol {
    namespace transform {
      namespace gw104 {

        /**
         * @brief GW104 ASDU Type5 处理类（文件重命名）
         * 专门处理文件重命名请求和响应
         */
        class AsduType5GWS : public AsduBase {
        public:
          AsduType5GWS() = default;
          virtual ~AsduType5GWS() = default;

          // 实现基类纯虚函数

          /**
           * @brief 解析协议帧为CommonMessage
           * @param frame 协议帧
           * @param common_msg 输出的CommonMessage
           * @return 成功返回0，失败返回错误码
           */
          virtual int ParseToCommonMessage(const base::ProtocolFrame& frame,
                                           base::CommonMessage& common_msg) override;

          /**
           * @brief 将CommonMessage转换为协议帧
           * @param common_msg 输入的CommonMessage
           * @param frame 输出的协议帧
           * @return 成功返回0，失败返回错误码
           */
          virtual int ConvertFromCommonMessage(const base::CommonMessage& common_msg,
                                               base::ProtocolFrame& frame) override;

          /**
           * @brief 获取支持的ASDU类型
           * @return ASDU类型标识
           */
          virtual uint8_t GetSupportedType() const override {
            return 5;  // Type 5 = 文件重命名
          }

          /**
           * @brief 获取ASDU类型描述
           * @return 类型描述字符串
           */
          virtual const char* GetTypeDescription() const override {
            return "GW104 ASDU Type5 - File Rename Request/Response";
          }

        private:
          // 私有辅助方法
          
          /**
           * @brief 解析重命名请求数据
           * @param data 原始数据
           * @param directory_path 输出的目录路径
           * @return 成功返回0，失败返回错误码
           */
          int parseRenameRequestData(const std::vector<uint8_t>& data, std::string& directory_path);
          
          /**
           * @brief 构建重命名响应数据
           * @param success 是否成功
           * @param message 响应消息
           * @param data 输出的数据
           * @return 成功返回0，失败返回错误码
           */
          int buildRenameResponseData(bool success, const std::string& message, std::vector<uint8_t>& data);
        };

      }  // namespace gw104
    }  // namespace transform
  }  // namespace protocol
}  // namespace zexuan

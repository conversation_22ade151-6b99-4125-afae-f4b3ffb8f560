/**
 * @file logging_performance.hpp
 * @brief 日志性能优化工具
 * <AUTHOR>
 * @date 2025-08-26
 */

#ifndef ZEXUAN_UTILS_LOGGING_PERFORMANCE_HPP
#define ZEXUAN_UTILS_LOGGING_PERFORMANCE_HPP

#include <spdlog/spdlog.h>
#include <chrono>
#include <atomic>

namespace zexuan {
namespace utils {

/**
 * @brief 日志性能优化工具类
 * 
 * 提供条件日志记录、频率限制等性能优化功能
 */
class LoggingPerformance {
public:
    /**
     * @brief 频率限制的日志记录
     * @param interval_ms 最小间隔时间（毫秒）
     * @param level 日志级别
     * @param format 格式字符串
     * @param args 参数
     */
    template<typename... Args>
    static void logWithRateLimit(int interval_ms, spdlog::level::level_enum level, 
                                 const std::string& format, Args&&... args) {
        static std::atomic<std::chrono::steady_clock::time_point> last_log_time{
            std::chrono::steady_clock::time_point{}
        };
        
        auto now = std::chrono::steady_clock::now();
        auto last = last_log_time.load();
        
        if (std::chrono::duration_cast<std::chrono::milliseconds>(now - last).count() >= interval_ms) {
            if (last_log_time.compare_exchange_weak(last, now)) {
                spdlog::log(level, format, std::forward<Args>(args)...);
            }
        }
    }
    
    /**
     * @brief 条件日志记录 - 只在条件满足时记录
     * @param condition 条件
     * @param level 日志级别
     * @param format 格式字符串
     * @param args 参数
     */
    template<typename... Args>
    static void logIf(bool condition, spdlog::level::level_enum level,
                      const std::string& format, Args&&... args) {
        if (condition) {
            spdlog::log(level, format, std::forward<Args>(args)...);
        }
    }
    
    /**
     * @brief 采样日志记录 - 只记录每N次中的一次
     * @param sample_rate 采样率（1表示每次都记录，10表示每10次记录一次）
     * @param level 日志级别
     * @param format 格式字符串
     * @param args 参数
     */
    template<typename... Args>
    static void logWithSampling(int sample_rate, spdlog::level::level_enum level,
                                const std::string& format, Args&&... args) {
        static std::atomic<int> counter{0};
        
        if (++counter % sample_rate == 0) {
            spdlog::log(level, format, std::forward<Args>(args)...);
        }
    }
};

} // namespace utils
} // namespace zexuan

// 便利宏定义
#define ZEXUAN_LOG_RATE_LIMIT(interval_ms, level, ...) \
    zexuan::utils::LoggingPerformance::logWithRateLimit(interval_ms, spdlog::level::level, __VA_ARGS__)

#define ZEXUAN_LOG_IF(condition, level, ...) \
    zexuan::utils::LoggingPerformance::logIf(condition, spdlog::level::level, __VA_ARGS__)

#define ZEXUAN_LOG_SAMPLE(rate, level, ...) \
    zexuan::utils::LoggingPerformance::logWithSampling(rate, spdlog::level::level, __VA_ARGS__)

// 高频路径专用宏
#define ZEXUAN_TRACE_IF(condition, ...) ZEXUAN_LOG_IF(condition, trace, __VA_ARGS__)
#define ZEXUAN_DEBUG_IF(condition, ...) ZEXUAN_LOG_IF(condition, debug, __VA_ARGS__)

#define ZEXUAN_TRACE_RATE_LIMIT(interval_ms, ...) ZEXUAN_LOG_RATE_LIMIT(interval_ms, trace, __VA_ARGS__)
#define ZEXUAN_DEBUG_RATE_LIMIT(interval_ms, ...) ZEXUAN_LOG_RATE_LIMIT(interval_ms, debug, __VA_ARGS__)

#define ZEXUAN_TRACE_SAMPLE(rate, ...) ZEXUAN_LOG_SAMPLE(rate, trace, __VA_ARGS__)
#define ZEXUAN_DEBUG_SAMPLE(rate, ...) ZEXUAN_LOG_SAMPLE(rate, debug, __VA_ARGS__)

#endif // ZEXUAN_UTILS_LOGGING_PERFORMANCE_HPP

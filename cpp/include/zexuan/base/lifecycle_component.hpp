#ifndef ZEXUAN_BASE_LIFECYCLE_COMPONENT_HPP
#define ZEXUAN_BASE_LIFECYCLE_COMPONENT_HPP

#include <atomic>
#include <mutex>
#include <string>
#include <vector>
#include <spdlog/spdlog.h>

namespace zexuan {
namespace base {

/**
 * @brief 组件状态枚举
 */
enum class ComponentState {
    STOPPED,        // 已停止（初始状态）
    STARTING,       // 启动中
    RUNNING,        // 运行中
    STOPPING,       // 停止中
    ERROR           // 错误状态
};

/**
 * @brief 生命周期组件接口
 * 
 * 所有需要生命周期管理的组件都应该实现此接口
 * 提供统一的Start/Stop两阶段生命周期管理
 */
class ILifecycleComponent {
public:
    virtual ~ILifecycleComponent() = default;
    
    /**
     * @brief 启动组件
     * 
     * Start阶段必须完成的工作：
     * 1. 读取配置文件
     * 2. 验证配置有效性
     * 3. 创建所有必要的成员对象
     * 4. 分配内存和其他资源
     * 5. 启动所有工作线程
     * 6. 建立网络连接
     * 7. 注册到Mediator（如果需要）
     * 8. 开始处理业务逻辑
     * 
     * @return true 启动成功，false 启动失败
     */
    virtual bool Start() = 0;
    
    /**
     * @brief 停止组件
     * 
     * Stop阶段必须完成的工作：
     * 1. 停止接收新的请求或任务
     * 2. 等待所有正在处理的任务完成（设置合理超时）
     * 3. 从Mediator注销（如果已注册）
     * 4. 关闭所有网络连接
     * 5. 停止所有工作线程（使用join等待）
     * 6. 释放所有分配的资源
     * 7. 清理临时文件和状态
     * 
     * @return true 停止成功，false 停止失败
     */
    virtual bool Stop() = 0;
    
    /**
     * @brief 获取组件当前状态
     * @return 组件状态
     */
    virtual ComponentState GetState() const = 0;
    
    /**
     * @brief 获取组件名称
     * @return 组件名称
     */
    virtual std::string GetComponentName() const = 0;
    
    /**
     * @brief 获取组件依赖列表（可选实现）
     * @return 依赖的组件名称列表
     */
    virtual std::vector<std::string> GetDependencies() const { return {}; }
};

/**
 * @brief 生命周期组件基类
 * 
 * 提供状态管理和状态转换的基础实现
 * 所有组件都应该继承此基类
 */
class LifecycleComponentBase : public ILifecycleComponent {
protected:
    std::atomic<ComponentState> state_{ComponentState::STOPPED};
    std::string component_name_;
    mutable std::mutex state_mutex_;
    
public:
    /**
     * @brief 构造函数
     * @param component_name 组件名称，用于日志标识
     */
    explicit LifecycleComponentBase(const std::string& component_name)
        : component_name_(component_name) {
    }
    
    /**
     * @brief 析构函数
     *
     * 析构函数规范：
     * - 析构函数中不能调用虚函数Stop()
     * - 子类必须在自己的析构函数中确保Stop()被调用
     * - 基类析构函数只做状态检查和警告
     */
    virtual ~LifecycleComponentBase() {
        if (GetState() != ComponentState::STOPPED) {
            spdlog::error("{}: Component not properly stopped before destruction! "
                         "This may cause resource leaks. Ensure Stop() is called in derived class destructor.",
                         component_name_);
        }
    }
    
    /**
     * @brief 获取组件当前状态
     * @return 组件状态
     */
    ComponentState GetState() const override {
        return state_.load();
    }
    
    /**
     * @brief 获取组件名称
     * @return 组件名称
     */
    std::string GetComponentName() const override {
        return component_name_;
    }
    
protected:
    /**
     * @brief 状态转换
     * 
     * 提供线程安全的状态转换，并验证转换的合法性
     * 
     * @param new_state 目标状态
     * @return true 转换成功，false 转换失败
     */
    bool TransitionTo(ComponentState new_state) {
        std::lock_guard<std::mutex> lock(state_mutex_);
        ComponentState current = state_.load();
        
        if (!IsValidTransition(current, new_state)) {
            spdlog::error("{}: Invalid state transition from {} to {}", 
                          component_name_, StateToString(current), StateToString(new_state));
            return false;
        }
        
        state_.store(new_state);
        spdlog::info("{}: State changed to {}", component_name_, StateToString(new_state));
        return true;
    }
    
private:
    /**
     * @brief 验证状态转换是否合法
     * @param from 源状态
     * @param to 目标状态
     * @return true 转换合法，false 转换非法
     */
    bool IsValidTransition(ComponentState from, ComponentState to) const {
        switch (from) {
            case ComponentState::STOPPED:
                return to == ComponentState::STARTING;
            case ComponentState::STARTING:
                return to == ComponentState::RUNNING || to == ComponentState::ERROR;
            case ComponentState::RUNNING:
                return to == ComponentState::STOPPING;
            case ComponentState::STOPPING:
                return to == ComponentState::STOPPED || to == ComponentState::ERROR;
            case ComponentState::ERROR:
                return to == ComponentState::STOPPING || to == ComponentState::STOPPED;
            default:
                return false;
        }
    }
    
    /**
     * @brief 将状态枚举转换为字符串
     * @param state 组件状态
     * @return 状态字符串
     */
    std::string StateToString(ComponentState state) const {
        switch (state) {
            case ComponentState::STOPPED: return "STOPPED";
            case ComponentState::STARTING: return "STARTING";
            case ComponentState::RUNNING: return "RUNNING";
            case ComponentState::STOPPING: return "STOPPING";
            case ComponentState::ERROR: return "ERROR";
            default: return "UNKNOWN";
        }
    }
};

}  // namespace base
}  // namespace zexuan

#endif  // ZEXUAN_BASE_LIFECYCLE_COMPONENT_HPP

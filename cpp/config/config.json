{"spdlog": {"level": "debug", "pattern": "[%Y-%m-%d %H:%M:%S.%e] [thread %t] [%^%l%$] %v", "max_file_size": 10485760, "max_files": 5, "flush_on_debug": true, "log_directory": "logs"}, "protocol": {"server": {"listen_address": "0.0.0.0", "listen_port": 8080, "max_connections": 100, "thread_pool_size": 4}, "gateway": {"request_timeout_seconds": 30, "thread_sleep_ms": 10, "max_pending_requests": 1000, "protocol_type": "gw104"}, "service": {"host": "127.0.0.1", "port": 8081}}, "bus": {"server": {"host": "0.0.0.0", "port": 8081, "thread_pool_size": 4, "verbose_logging": true, "max_connections": 1000}, "client": {"server_host": "127.0.0.1", "server_port": 8081}}, "devices": {"rename_client": {"clientid": 10, "thread_pool_size": 32}, "tcp_bus_client": {"clientid": 9}}}